```java
select row_number() over(order by cc.<PERSON>N<PERSON>ber,cc.guige,cc.flengh,cc.fdj,cc.remark,cc.fnotetext,cc.qx) FIDENTITYID
   ,cc.gongsi,cc.store,cc.MaterialNumber,cc.PM,
     cc.GUIGE,cc.flengh,cc.fdj,cc.yddlxr, 
    sum(cc.PCS) pcs, 
    sum(cc.SL) sl, 
    cc.QX, 
    cc.REMARK,SUBSTR(
    cc.fnotetext,
    1,
    CASE 
        WHEN LENGTHB(cc.fnotetext) <= 355 THEN LENGTH(cc.fnotetext)
        ELSE FLOOR(355 / 3)
    END
) fnotetext,CASE 
    WHEN sum(cc.PCS) = 0 THEN 0
    ELSE round(sum(cc.days)/sum(cc.PCS),0)
END as days,case when cc.FDocumentStatus is null  then '未拣' else '已拣' end barcodeStatus   from (


  select aa.orgName gongsi,aa.stockName store,aa.MaterialNumber,aa.PM,aa.sl, aa.guige,aa.flengh, aa.fdj, aa.pcs, aa.qx, aa.remark,aa.fnotetext, 
 T_BAS_AssistantDataEntry_l.fdatavalue as YDDLXR,aa.days,aa.FDocumentStatus  from(
     select T_ORG_Organizations_L.fname orgName,T_BD_MATERIAL_L.FNAME as PM, WL.FNUMBER as MaterialNumber,T_BD_STOCK_L.fname stockName,
     (Select T_BAS_AssistantDataEntry_l.FDataValue   
      from T_BD_FLEXSITEMDETAILV LEFT JOIN T_BAS_AssistantDataEntry   
      ON T_BAS_AssistantDataEntry.FENTRYID = T_BD_FLEXSITEMDETAILV.FF100501  
      LEFT JOIN T_BAS_AssistantDataEntry_l   
     ON T_BAS_AssistantDataEntry.FENTRYID = T_BAS_AssistantDataEntry_l.FENTRYID   
     and T_BAS_AssistantDataEntry_l.FLocaleId = 2052   
     Where T_BD_FLEXSITEMDETAILV.Fid = T_BD_BARCODEMAIN.FAUXPROPID) AS GUIGE, 
 T_BD_BARCODEMAIN.flengh,
    T_BD_BARCODEMAIN.Fdj,sum(T_BD_BARCODEMAIN.FAUXILIARYQTY) as PCS, 
    T_BD_BARCODEMAIN.F_PAEZ_YDDLXR,sum(T_BD_BARCODEMAIN.FQty) as SL, 
    ltrim(rtrim(T_BAS_AssistantDataEntry_l.FDATAVALUE))||ltrim(rtrim(T_BD_BARCODEMAIN.FDET_MXYQ1)) as QX, 
sum(sysdate-T_BD_BARCODEMAIN.FCREATEDATE) days,
    ltrim(rtrim(T_BD_BARCODEMAIN.FREMARK)) as REMARK ,
    ltrim(rtrim(T_BD_BARCODEMAIN.fnotetext)) as fnotetext,dd.FDocumentStatus   
    from T_BD_BARCODEMAIN LEFT OUTER JOIN T_BAS_AssistantDataEntry_l  
     ON T_BAS_AssistantDataEntry_l.FENTRYID = T_BD_BARCODEMAIN.F_PAEZ_QY  
  LEFT