info: Andafa.Native.DataBase.Database[0]
      SQL:select af_auid()
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|开始执行
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|主机：27a241a3d191
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|开始拉取接口配置信息
info: Andafa.Native.DataBase.Database[0]
      SQL:SELECT sit.*,site.io_last_data_time,site.io_last_succeed_time FROM ss_io_tbl sit left join ss_io_tbl_ext site on sit.io_tbl_id = site.io_tbl_id where sit.io_tbl_id=@io_tbl_id
info: Andafa.Native.DataBase.Database[0]
      SQL:SELECT * FROM ss_io_tbl_ext where io_tbl_id=@io_tbl_id
info: Andafa.Native.DataBase.Database[0]
      SQL:UPDATE ss_io_tbl_ext SET io_run_times=io_run_times+@io_run_times WHERE io_tbl_id=@io_tbl_id
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|开始拉取读授权
info: Andafa.Native.DataBase.Database[0]
      SQL:SELECT * FROM ss_io_auth where io_auth_id=@io_auth_id
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|开始拉取写授权
info: Andafa.Native.DataBase.Database[0]
      SQL:SELECT * FROM ss_io_auth where io_auth_id=@io_auth_id
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|开始拉取字段映射
info: Andafa.Native.DataBase.Database[0]
      SQL:SELECT * FROM ss_io_tblf where io_tbl_id=@io_tbl_id
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|初始化V8引擎
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|开始验证金蝶授权
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|{"format":1,"useragent":"ApiClient","rid":"4ddc6476-b499-4270-97cc-821235f14700","parameters":["62e096c858caf7","倪步莹","nbynbynby123.","2052"],"timestamp":"2025-09-01 15:40:21.974921","v":"1.0"}
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|{"Message":null,"MessageCode":"CheckPasswordPolicy","LoginResultType":1,"Context":{"UserLocale":"zh-CN","LogLocale":"zh-CN","DBid":"62e096c858caf7","DatabaseType":2,"SessionId":"dvmlt2n1ppqwhmg5mtdkrugu","UseLanguages":[{"LocaleId":2052,"LocaleName":"中文(简体)","Alias":"CN","LicenseType":0}],"UserId":********,"UserName":"倪步莹","CustomName":"温州市金田塑业有限公司","DisplayVersion":"7.7.2346.1","DataCenterName":"43测试","UserToken":"73ea9264-f215-4376-98f7-a34b482ce1cb","CurrentOrganizationInfo":{"ID":101203,"AcctOrgType":"1","Name":"温州市金田塑业有限公司","FunctionIds":[101,102,103,104,107,108,109,110,111,112,113]},"IsCH_ZH_AutoTrans":false,"ClientType":32,"WeiboAuthInfo":{"WeiboUrl":null,"NetWorkID":null,"CompanyNetworkID":null,"Account":" @","AppKey":"FkdTqJiNeCQC0ugp","AppSecret":"yCP3ucK2IQUm2D3heHxiarq1RJZwfcnKullRSMOIEM","TokenKey":" ","TokenSecret":" ","Verify":null,"CallbackUrl":null,"UserId":" ","Charset":{"BodyName":"utf-8","EncodingName":"Unicode (UTF-8)","HeaderName":"utf-8","WebName":"utf-8","WindowsCodePage":1200,"IsBrowserDisplay":true,"IsBrowserSave":true,"IsMailNewsDisplay":true,"IsMailNewsSave":true,"IsSingleByte":false,"EncoderFallback":{"DefaultString":"�","MaxCharCount":1},"DecoderFallback":{"DefaultString":"�","MaxCharCount":1},"IsReadOnly":true,"CodePage":65001}},"UTimeZone":{"OffsetTicks":************,"StandardName":"(UTC+08:00)北京，重庆，香港特别行政区，乌鲁木齐","Id":230,"Number":"1078_SYS","CanBeUsed":true},"STimeZone":{"OffsetTicks":************,"StandardName":"(UTC+08:00)北京，重庆，香港特别行政区，乌鲁木齐","Id":230,"Number":"1078_SYS","CanBeUsed":true},"GDCID":"2659be84-5cb7-4698-8ff2-0ead54088d44","Gsid":null,"TRLevel":0,"ProductEdition":0,"DataCenterNumber":"A01","ContextResultType":0},"KDSVCSessionId":"6747fa27-644b-4432-acce-a7f3864e1a8d","FormId":null,"RedirectFormParam":null,"FormInputObject":null,"ErrorStackTrace":null,"Lcid":0,"AccessToken":null,"CustomParam":{"GlobalWatermarkConfigStr":"eyJ3aWR0aCI6MzAwLCJoZWlnaHQiOjE2MCwiYW5nbGUiOi0xNSwid2F0ZXJtYXJrdGV4dCI6IiAiLCJsaWN0ZXh0IjoiIiwib3BhY2l0eSI6MC4xLCJmb250ZmFtaWx5IjoiTWljcm9zb2Z0IFlhSGVpIiwiZm9udHNpemUiOjE0LCJiaWdfZm9udHNpemUiOjE2LCJzaG93dHlwZSI6MH0="},"KdAccessResult":null,"IsSuccessByAPI":true}
Cache-Control: private
Server: Microsoft-IIS/10.0
X-AspNet-Version: 4.0.30319
Set-Cookie: kdservice-sessionid=6747fa27-644b-4432-acce-a7f3864e1a8d; path=/, ASP.NET_SessionId=dvmlt2n1ppqwhmg5mtdkrugu; path=/; HttpOnly; SameSite=Lax
X-Powered-By: ASP.NET
Date: Mon, 01 Sep 2025 07:40:22 GMT
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|开始清空！
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|Database:PostgreSQL，SQL:truncate table pd_part_temp，
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|清空完毕
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|读取前的脚本执行完成
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|cookieskdservice-sessionid=6747fa27-644b-4432-acce-a7f3864e1a8d; path=/, ASP.NET_SessionId=dvmlt2n1ppqwhmg5mtdkrugu; path=/; HttpOnly; SameSite=Lax
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|0001-01-01 00:00:00.000000
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|读取数据结果:
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|[[{"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":null,"Message":"元数据中标识为MaterialNumber的字段不存在","DIndex":0}],"SuccessEntitys":[],"SuccessMessages":[],"MsgCode":9}}}]]
fail: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|读取后的脚本Error:[object Object]
      Inner Exceptions:
      [object Object]
      
      
info: AndafaAx.ExternalInterface.MainX[0]
      250901000058384884|任务结束
info: Andafa.Native.DataBase.Database[0]
      SQL:INSERT INTO ss_log_run
      (log_run_id, log_run_batch, log_start_time, log_end_time, log_object_id, log_type_id, log_action, has_error, log_summary, crt_time, crt_user, crt_user_no, crt_user_name, crt_host)
      VALUES(@log_run_id,@log_run_batch,@log_start_time,@log_end_time,@log_object_id,@log_type_id,@log_action,@has_error,@log_summary,@crt_time,@crt_user,@crt_user_no,@crt_user_name,@crt_host)
info: Andafa.Native.DataBase.Database[0]
      SQL:select af_auid()
info: Andafa.Native.DataBase.Database[0]
      SQL:INSERT INTO ss_log_run_process (log_run_process_id, log_run_id, log_seq, log_run_prcess, crt_time) VALUES ( @log_run_process_id,  @log_run_id,  @log_seq,  @log_run_prcess,  @crt_time)
info: Andafa.Native.DataBase.Database[0]
      SQL:UPDATE ss_log_run set has_error=@has_error WHERE log_run_id=@log_run_id
info: Andafa.Native.DataBase.Database[0]
      SQL:select af_auid()
info: Andafa.Native.DataBase.Database[0]
      SQL:INSERT INTO ss_log_run_error (log_run_error_id, log_run_id, log_seq, log_run_error, crt_time) VALUES ( @log_run_error_id,  @log_run_id,  @log_seq,  @log_run_error,  @crt_time)