```java
header.AddChild("gongsi", new LocaleValue("库存组织", this.Context.UserLocale.LCID));
header.AddChild("store", new LocaleValue("仓库名称", this.Context.UserLocale.LCID));
header.AddChild("MaterialNumber", new LocaleValue("物料编码", this.Context.UserLocale.LCID));
header.AddChild("yddlxr", new LocaleValue("预订单联系人", this.Context.UserLocale.LCID));
header.AddChild("pm", new LocaleValue("物料名称", this.Context.UserLocale.LCID));
header.AddChild("fdj", new LocaleValue("等级", this.Context.UserLocale.LCID));

header.AddChild("guige", new LocaleValue("规格型号", this.Context.UserLocale.LCID));
header.AddChild("fLengh", new LocaleValue("长度", this.Context.UserLocale.LCID));
var sl = header.AddChild("sl", new LocaleValue("数量"), SqlStorageType.SqlDecimal);
//header.AddChild("pcs", new LocaleValue("件数", this.Context.UserLocale.LCID));
var pcs = header.AddChild("pcs", new LocaleValue("件数"), SqlStorageType.SqlDecimal);
header.AddChild("qx", new LocaleValue("缺陷", this.Context.UserLocale.LCID));
header.AddChild("remark", new LocaleValue("备注", this.Context.UserLocale.LCID));
header.AddChild("fnotetext", new LocaleValue("订单备注", this.Context.UserLocale.LCID));
//var billNo = header.AddChild("DJBH", new LocaleValue("单据编号"));
//header.AddChild("sl", new LocaleValue("数量", SqlStorageType.SqlDecimal));
header.AddChild("days", new LocaleValue("天数", this.Context.UserLocale.LCID)); 
header.AddChild("barcodeStatus", new LocaleValue("出库状态", this.Context.UserLocale.LCID));

字段对应关系
store             invp_name     仓库编号
gongsi            company_name  公司编号
MaterialNumber    part_no   料号
guige             part_idt  规格
fLengh            part_length   长度
remark            ps_jumboroll_inv_rmk01 备注
fnotetext（不存在）         ps_jumboroll_inv_rmk02  订单备注

select af_ps_jumboroll_inv_tmp_after();